"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { Card, Spin, Table, Button, Space, Modal, message, Tag, Tooltip, Popconfirm, Typography, Statistic, Row, Col, Input } from "antd";
import { EditOutlined, DeleteOutlined, CheckCircleOutlined, PlusOutlined,ContactsOutlined,UserOutlined,TeamOutlined,ReloadOutlined } from "@ant-design/icons";

// Services and Types
import { Partner } from '@/services/ims/partner';
import { getPartnerList,getPartner,addPartner,editPartner,deletePartner } from '@/services/ims/PartnerService';

// Components
import PartnerFormModal from '@/app/ims/components/PartnerFormModal';

// Utils
import { safeString } from '@/utils/dataValidation';

const { Title } = Typography;

// Stats Interface
interface StatsData {
  totalPartners: number;
  activePartners: number;
  individualPartners: number;
  organizationPartners: number;
}

const PartnerPage = () => {
  // State
  const [partners, setPartners] = useState<Partner[]>([]);
  const [stats, setStats] = useState<StatsData>({
    totalPartners: 0,
    activePartners: 0,
    individualPartners: 0,
    organizationPartners: 0,
  });
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [filteredPartners, setFilteredPartners] = useState<Partner[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Calculate stats
  const calculateStats = useCallback((partnersData: Partner[]) => {
    setStats({
      totalPartners: partnersData.length,
      activePartners: partnersData.filter(p => !p.isDeleted).length, // Assuming isDeleted exists and indicates active
      individualPartners: partnersData.filter(p => p.individualDetail != null).length,
      organizationPartners: partnersData.filter(p => p.organizationDetail != null).length,
    });
  }, []);

  // Load data
  const loadAllData = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getPartnerList();
      if (res.success && res.data) {
        setPartners(res.data);
        setFilteredPartners(res.data); // Initially, filtered is all
        calculateStats(res.data);
      } else {
        message.error(res.message || '載入商業夥伴資料失敗');
      }
    } catch (error) {
      console.error('載入資料時發生錯誤:', error);
      message.error('載入資料失敗，請重試');
    } finally {
      setLoading(false);
    }
  }, [calculateStats]);

  // Initial load
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  // Search logic
  useEffect(() => {
    const keyword = searchKeyword.toLowerCase().trim();
    if (!keyword) {
        setFilteredPartners(partners);
        return;
    }
    const filtered = partners.filter(partner =>
        safeString(partner.partnerId).toLowerCase().includes(keyword) ||
        (partner.individualDetail && safeString(partner.individualDetail.lastName).toLowerCase().includes(keyword)) ||
        (partner.individualDetail && safeString(partner.individualDetail.firstName).toLowerCase().includes(keyword)) ||
        (partner.organizationDetail && safeString(partner.organizationDetail.companyName).toLowerCase().includes(keyword))
    );
    setFilteredPartners(filtered);
  }, [partners, searchKeyword]);


  // CRUD Operations
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const response = selectedPartner
        ? await editPartner({ ...values, partnerId: selectedPartner.partnerId })
        : await addPartner(values);

      if (response.success) {
        message.success(selectedPartner ? '更新成功' : '新增成功');
        setIsModalVisible(false);
        setSelectedPartner(null);
        await loadAllData();
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error('提交商業夥伴資料時發生錯誤:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (partner: Partner) => {
    setSelectedPartner(partner);
    setIsModalVisible(true);
  };

  const handleDelete = async (partnerId: string) => {
    Modal.confirm({
      title: '確認刪除',
      content: '確定要刪除此商業夥伴嗎？此操作無法復原。',
      okText: '確定刪除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        const response = await deletePartner(partnerId);
        if (response.success) {
          message.success('刪除成功');
          await loadAllData();
        } else {
          message.error(response.message || '刪除失敗');
        }
      }
    });
  };

  // Table columns
  const columns = useMemo(() => [
    {
      title: '夥伴ID',
      dataIndex: 'partnerId',
      key: 'partnerId',
      width: 150,
      sorter: (a: Partner, b: Partner) => safeString(a.partnerId).localeCompare(safeString(b.partnerId)),
      render: (text: string) => <span style={{ fontWeight: 500 }}>{text || '-'}</span>,
    },
    {
      title: '夥伴名稱',
      key: 'name',
      sorter: (a: Partner, b: Partner) => safeString(a.organizationDetail?.companyName || a.individualDetail?.lastName).localeCompare(safeString(b.organizationDetail?.companyName || b.individualDetail?.lastName)),
      render: (record: Partner) => (
        <span style={{ fontWeight: 500, color: '#1890ff', cursor: 'pointer' }}
              onClick={() => handleEdit(record)}>
          {record.organizationDetail?.companyName || `${record.individualDetail?.lastName}${record.individualDetail?.firstName}` || '-'}
        </span>
      ),
    },
    {
      title: '類型',
      key: 'type',
      width: 100,
      render: (record: Partner) => (
        <Tag color={record.individualDetail ? 'blue' : 'green'} icon={record.individualDetail ? <UserOutlined /> : <TeamOutlined />}>
          {record.individualDetail ? '個人' : '組織'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      fixed: 'right' as const,
      render: (record: Partner) => (
        <Space size="small">
          <Tooltip title="編輯">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="刪除">
            <Popconfirm
              title="確定要刪除此商業夥伴嗎？"
              onConfirm={() => handleDelete(record.partnerId)}
              okText="確定"
              cancelText="取消"
            >
              <Button
                type="link"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ], [handleEdit, handleDelete]);

  return (
    <div style={{ padding: '24px' }}>
      <Spin spinning={loading}>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '12px' }}>
            <ContactsOutlined style={{ color: '#1890ff' }} />
            商業夥伴管理
          </Title>
          <Typography.Text type="secondary">
            管理所有商業夥伴資料
          </Typography.Text>
        </div>

        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="總夥伴數" value={stats.totalPartners} prefix={<ContactsOutlined />} /></Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="活躍夥伴" value={stats.activePartners} prefix={<CheckCircleOutlined />} /></Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="個人夥伴" value={stats.individualPartners} prefix={<UserOutlined />} /></Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="組織夥伴" value={stats.organizationPartners} prefix={<TeamOutlined />} /></Card>
          </Col>
        </Row>

        <Card size="small" style={{ marginBottom: '16px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setSelectedPartner(null);
                    setIsModalVisible(true);
                  }}
                >
                  新增夥伴
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadAllData}
                >
                  重新載入
                </Button>
              </Space>
            </Col>
            <Col>
                <Input.Search
                    placeholder="搜尋夥伴ID或名稱..."
                    onSearch={(value) => setSearchKeyword(value)}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    style={{ width: 250 }}
                    allowClear
                />
            </Col>
          </Row>
        </Card>

        <Card >
          <Table
            columns={columns}
            dataSource={filteredPartners}
            rowKey="partnerId"
            loading={loading}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: filteredPartners.length,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size);
              },
            }}
            scroll={{ x: 800 }}
            size={isMobile ? 'small' : 'middle'}
          />
        </Card>
      </Spin>

      <PartnerFormModal
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
          setSelectedPartner(null);
        }}
        selectedPartner={selectedPartner}
        onSubmit={handleSubmit}
        loading={loading}
      />
    </div>
  );
};

export default PartnerPage;