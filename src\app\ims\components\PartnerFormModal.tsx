"use client";

import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, Row, Col, Tabs, Button, message } from 'antd';
import { UserOutlined, TeamOutlined, ContactsOutlined } from '@ant-design/icons';
import { Partner } from '@/services/ims/partner';

const { TabPane } = Tabs;

interface PartnerFormModalProps {
  visible: boolean;
  onClose: () => void;
  selectedPartner: Partner | null;
  onSubmit: (values: any) => Promise<void>;
  loading?: boolean;
}

const PartnerFormModal: React.FC<PartnerFormModalProps> = ({
  visible,
  onClose,
  selectedPartner,
  onSubmit,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const [partnerType, setPartnerType] = useState<'individual' | 'organization'>('individual');

  useEffect(() => {
    if (visible) {
      if (selectedPartner) {
        const type = selectedPartner.organizationDetail ? 'organization' : 'individual';
        setPartnerType(type);
        form.setFieldsValue({
          individualDetail: selectedPartner.individualDetail,
          organizationDetail: selectedPartner.organizationDetail,
        });
      } else {
        setPartnerType('individual');
        form.resetFields();
      }
    }
  }, [visible, selectedPartner, form]);

  const handleFormSubmit = async (values: any) => {
    try {
      const submitData: Partial<Partner> = {
        partnerId: selectedPartner?.partnerId, // Include partnerId if editing
      };

      if (partnerType === 'individual') {
        submitData.individualDetail = {
          ...values.individualDetail,
          partnerId: selectedPartner?.partnerId || '', // Ensure partnerId is set for individualDetail
        };
      } else if (partnerType === 'organization') {
        submitData.organizationDetail = {
          ...values.organizationDetail,
          partnerId: selectedPartner?.partnerId || '', // Ensure partnerId is set for organizationDetail
        };
      }
      await onSubmit(submitData);
    } catch (error) {
      console.error('❌ PartnerFormModal: Error submitting form:', error);
      message.error('提交失敗，請重試');
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ContactsOutlined style={{ color: '#1890ff' }} />
          <span>{selectedPartner ? '編輯商業夥伴' : '新增商業夥伴'}</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      onOk={() => form.submit()}
      confirmLoading={loading}
      destroyOnClose
      width={800}
    >
      <Form form={form} layout="vertical" onFinish={handleFormSubmit} initialValues={{}}>
        <Tabs activeKey={partnerType} onChange={(key) => setPartnerType(key as any)}>
          <TabPane tab={<span><UserOutlined />個人</span>} key="individual">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="姓氏" name={['individualDetail', 'lastName']} rules={[{ required: true, message: '請輸入姓氏' }]}>
                  <Input placeholder="請輸入姓氏" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="名字" name={['individualDetail', 'firstName']} rules={[{ required: true, message: '請輸入名字' }]}>
                  <Input placeholder="請輸入名字" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="身分證號" name={['individualDetail', 'identificationNumber']}>
                  <Input placeholder="請輸入身分證號" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="生日" name={['individualDetail', 'birthDate']}>
                  <Input placeholder="YYYY-MM-DD" />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>
          <TabPane tab={<span><TeamOutlined />組織</span>} key="organization">
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="公司名稱" name={['organizationDetail', 'companyName']} rules={[{ required: true, message: '請輸入公司名稱' }]}>
                  <Input placeholder="請輸入公司名稱" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="統一編號" name={['organizationDetail', 'bussinessId']}>
                  <Input placeholder="請輸入統一編號" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="稅務編號" name={['organizationDetail', 'taxId']}>
                  <Input placeholder="請輸入稅務編號" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="負責人" name={['organizationDetail', 'responsiblePerson']}>
                  <Input placeholder="請輸入負責人" />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Form>
    </Modal>
  );
};

export default PartnerFormModal;