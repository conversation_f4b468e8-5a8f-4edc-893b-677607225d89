// =========================================================================================
// Service for Partner Management - Interacting with the Partner API
// =========================================================================================

import { Partner } from '@/services/ims/partner';
import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";

/**
 * Retrieves a list of all partners from the backend.
 * @returns A promise that resolves to an ApiResponse containing an array of Partner objects.
 */
export async function getPartnerList(): Promise<ApiResponse<Partner[]>> {
    try {
        const response = await httpClient(apiEndpoints.getPartnerList, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        console.error('Error fetching partner list:', error);
        return {
            success: false,
            message: error.message || "搜尋商業夥伴列表失敗",
            data: []
        };
    }
}

/**
 * Fetches a single partner by their unique ID.
 * @param id The ID of the partner to retrieve.
 * @returns A promise that resolves to an ApiResponse containing a Partner object.
 */
export async function getPartner(id: string): Promise<ApiResponse<Partner>> {
    try {
        const response = await httpClient(`${apiEndpoints.getPartner}/${id}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        console.error(`Error fetching partner with ID ${id}:`, error);
        return {
            success: false,
            message: error.message || `搜尋商業夥伴失敗 (ID: ${id})`,
        };
    }
}

/**
 * Creates a new partner record.
 * @param partnerData The data for the new partner.
 * @returns A promise that resolves to an ApiResponse.
 */
export async function addPartner(partnerData: Partial<Partner>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addPartner, {
            method: "POST",
            body: JSON.stringify(partnerData),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        console.error('Error creating partner:', error);
        return {
            success: false,
            message: error.message || "新增商業夥伴失敗",
        };
    }
}

/**
 * Updates an existing partner's information.
 * @param partnerData The updated data for the partner.
 * @returns A promise that resolves to an ApiResponse.
 */
export async function editPartner(partnerData: Partial<Partner>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editPartner, {
            method: "PUT",
            body: JSON.stringify(partnerData),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        console.error(`Error updating partner with ID ${partnerData.partnerId}:`, error);
        return {
            success: false,
            message: error.message || `編輯商業夥伴失敗 (ID: ${partnerData.partnerId})`,
        };
    }
}

/**
 * Deletes a partner from the system.
 * @param id The ID of the partner to delete.
 * @returns A promise that resolves to an ApiResponse.
 */
export async function deletePartner(id: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deletePartner, {
            method: "DELETE",
            body: JSON.stringify({ partnerID: id }),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        console.error(`Error deleting partner with ID ${id}:`, error);
        return {
            success: false,
            message: error.message || `刪除商業夥伴失敗 (ID: ${id})`,
        };
    }
}