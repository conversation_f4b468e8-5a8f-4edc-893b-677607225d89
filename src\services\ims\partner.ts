// =========================================================================================
// DTOs for Partner Management - Aligning with Backend Models
// =========================================================================================

/**
 * Base DTO for entities, providing common properties like creation and modification dates.
 */
export interface ModelBaseEntityDTO {
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

// -----------------------------------------------------------------------------------------
// Region: Partner Core and Detail DTOs
// -----------------------------------------------------------------------------------------

/**
 * Represents a comprehensive view of a business partner, aggregating all related details.
 */
export interface Partner extends ModelBaseEntityDTO {
  partnerId: string;
  individualDetail?: IndividualDetail;
  organizationDetail?: OrganizationDetail;
  customerDetail?: CustomerDetail;
  supplierDetail?: SupplierDetail;
  partnerContacts: PartnerContact[];
  addresses: PartnerAddress[];
}

/**
 * Details for a partner who is an individual.
 */
export interface IndividualDetail {
  partnerId: string;
  lastName?: string;
  firstName?: string;
  identificationNumber?: string;
  birthDate?: Date;
}

/**
 * Details for a partner that is an organization.
 */
export interface OrganizationDetail {
  partnerId: string;
  companyName?: string;
  bussinessId?: string;
  taxId?: string;
  responsiblePerson?: string;
}

/**
 * Customer-specific details for a partner.
 */
export interface CustomerDetail {
  partnerId: string;
  customerCode?: string;
  customerCategoryId?: string;
  customerCategory?: CustomerCategory; // Assuming this will be expanded with more fields
  settlementDay?: number;
}

/**
 * Supplier-specific details for a partner.
 */
export interface SupplierDetail {
  partnerId: string;
  supplierCode?: string;
  supplierCategoryId?: string;
  supplierCategory?: SupplierCategory; // Assuming this will be expanded with more fields
  settlementDay?: number;
}

/**
 * Defines the relationship between a partner and a contact person.
 */
export interface PartnerContact {
  partnerId: string;
  contactId: string;
  contactRoleId: string;
  isPrimary: boolean;
}

/**
 * Address information for a partner, supporting both local and international formats.
 */
export interface PartnerAddress {
  partnerAddressId: string;
  partnerId: string;
  address: string;
  city?: string;
  district?: string;
  postalCode?: string;
  country?: string;
  isPrimary: boolean;
}

// -----------------------------------------------------------------------------------------
// Region: Supporting DTOs (Placeholders for now)
// -----------------------------------------------------------------------------------------

/**
 * Placeholder for Customer Category information.
 * This should be populated with actual fields as the feature develops.
 */
export interface CustomerCategory {
  id: string;
  name: string;
  // Add other relevant properties like description, etc.
}

/**
 * Placeholder for Supplier Category information.
 * This should be populated with actual fields as the feature develops.
 */
export interface SupplierCategory {
  id: string;
  name: string;
  // Add other relevant properties like description, etc.
}